# AI Hamli Sortex Rate Editor

## Overview

The AI Hamli Sortex Rate Editor module allows users to manually edit hamli and sortex rates in any manufacturing order state, with costs calculated as rate × total weight. It is especially useful for adjusting rates after manufacturing is completed when actual rates are known.

## Features

### Core Functionality
- **Manual Rate Override**: Edit hamli and sortex rates manually in any manufacturing order state
- **Rate-Based Calculation**: Final cost = Manual Rate × Total Weight (kg)
- **Post-Production Editing**: Especially useful for adjusting rates after manufacturing is completed
- **Automatic Recalculation**: All dependent costs (CI landed cost, total cost) automatically update when manual rates are changed
- **Dual Mode Operation**: Switch between automatic rate calculation and manual rate override modes
- **Audit Trail**: Track who modified rates and when

### Cost Integration
- **Seamless Integration**: Works with existing cost calculation system from `ai_bt_spices_module`
- **Dependent Cost Updates**: CI landed cost and total cost automatically recalculate using final costs
- **Production B Support**: Respects Production B logic where sortex costs don't apply

### Security & Access Control
- **Role-Based Access**: Two security groups for different permission levels
- **Flexible State Access**: Cost overrides can be enabled in any manufacturing order state
- **Input Validation**: Prevents negative cost entries

## Installation

1. Ensure `ai_bt_spices_module` is installed (required dependency)
2. Install this module through Odoo Apps or by placing it in your addons directory
3. Update the module list and install `ai_hamli_sortex`

## Usage

### Enabling Cost Override

1. Navigate to any Manufacturing Order (any state)
2. Go to the "Hamli & Sortex Cost Override" tab
3. Click "Enable Override" for hamli or sortex costs
4. The manual cost field will be initialized with the current automatic calculation
5. Edit the manual cost value as needed
6. All dependent costs will automatically recalculate

### Disabling Cost Override

1. Click "Disable Override" to return to automatic calculation
2. Manual values will be cleared and automatic calculation restored
3. All dependent costs will recalculate using automatic values

### Cost Recalculation

- **Recalculate Costs**: Force recalculation of all dependent costs with current override values

## Security Groups

### Hamli Sortex Cost Editor
- Can enable/disable cost overrides
- Can edit manual cost values
- Can recalculate costs

### Hamli Sortex Cost Manager  
- All editor permissions
- Can view audit trails
- Can reset all overrides
- Can manage cost overrides for all users

## Technical Details

### New Fields Added

**Override Control Fields:**
- `hamli_cost_override_mode`: Boolean to enable/disable hamli cost override
- `sortex_cost_override_mode`: Boolean to enable/disable sortex cost override

**Manual Cost Fields:**
- `hamli_cost_manual`: User-entered hamli cost value
- `sortex_cost_manual`: User-entered sortex cost value

**Cost Override Integration:**
- Overrides the original `hamali_cost` and `sortex_landed_cost` computed fields
- When override mode is enabled, these fields return the manual values
- When override mode is disabled, these fields return the automatic calculations

**Audit Trail Fields:**
- `hamli_cost_last_modified_by`: User who last modified hamli cost
- `hamli_cost_last_modified_date`: Date of last hamli cost modification
- `sortex_cost_last_modified_by`: User who last modified sortex cost  
- `sortex_cost_last_modified_date`: Date of last sortex cost modification

### Cost Calculation Override

The module overrides the original computed fields to use manual values when override mode is enabled:
- `_compute_hamali_cost()`: Returns manual value when override enabled, otherwise automatic calculation
- `_compute_sortex_landed_cost()`: Returns manual value when override enabled, otherwise automatic calculation
- All dependent calculations (total cost, CI landed cost) automatically use the overridden values

### Compatibility

- **Backward Compatible**: Existing automatic calculations continue to work
- **Production B Support**: Sortex overrides disabled for Production B orders
- **Flexible Access**: Overrides available in any manufacturing order state

## API Methods

### Public Methods

- `action_enable_hamli_override()`: Enable hamli cost override mode
- `action_enable_sortex_override()`: Enable sortex cost override mode
- `action_disable_hamli_override()`: Disable hamli cost override mode
- `action_disable_sortex_override()`: Disable sortex cost override mode
- `action_recalculate_costs()`: Recalculate all costs with current overrides

### Utility Methods

- `_get_effective_hamali_cost()`: Get the effective hamali cost (override or computed)
- `_get_effective_sortex_cost()`: Get the effective sortex cost (override or computed)

## Dependencies

- `base`: Odoo base module
- `mrp`: Manufacturing module
- `ai_bt_spices_module`: Required for base hamli/sortex functionality

## Version

- **Version**: 18.0.1.0.0
- **Odoo Version**: 18.0 CE (Community Edition)
- **Modern Syntax**: Uses Odoo 18 CE modern view syntax (no deprecated `attrs`)
- **License**: LGPL-3
