from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)


class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    # Override mode fields
    hamli_cost_override_mode = fields.<PERSON><PERSON>an(
        string='Hamli Cost Override Mode',
        default=False,
        help='When enabled, allows manual editing of hamli cost instead of automatic calculation'
    )
    sortex_cost_override_mode = fields.Boolean(
        string='Sortex Cost Override Mode', 
        default=False,
        help='When enabled, allows manual editing of sortex cost instead of automatic calculation'
    )
    
    # Manual override fields (rates)
    hamli_rate_manual = fields.Float(
        string='Manual Hamli Rate',
        default=0.0,
        help='Manually entered hamli rate per kg that overrides automatic rate'
    )
    sortex_rate_manual = fields.Float(
        string='Manual Sortex Rate',
        default=0.0,
        help='Manually entered sortex rate per kg that overrides automatic rate'
    )
    

    
    # Audit trail fields
    hamli_cost_last_modified_by = fields.Many2one(
        'res.users',
        string='Hamli Cost Last Modified By',
        readonly=True
    )
    hamli_cost_last_modified_date = fields.Datetime(
        string='Hamli Cost Last Modified Date',
        readonly=True
    )
    sortex_cost_last_modified_by = fields.Many2one(
        'res.users', 
        string='Sortex Cost Last Modified By',
        readonly=True
    )
    sortex_cost_last_modified_date = fields.Datetime(
        string='Sortex Cost Last Modified Date',
        readonly=True
    )



    def action_enable_hamli_override(self):
        """Enable hamli rate override mode - can be used in any state"""
        self.ensure_one()

        self.write({
            'hamli_cost_override_mode': True,
            'hamli_rate_manual': self.hamali_rate,  # Initialize with current automatic rate
            'hamli_cost_last_modified_by': self.env.user.id,
            'hamli_cost_last_modified_date': fields.Datetime.now(),
        })

        # Force immediate recalculation
        self._compute_hamali_cost()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Hamli rate override mode enabled. You can now edit the hamli rate manually.'),
                'sticky': False,
                'type': 'success',
            }
        }

    def action_enable_sortex_override(self):
        """Enable sortex rate override mode - can be used in any state"""
        self.ensure_one()

        self.write({
            'sortex_cost_override_mode': True,
            'sortex_rate_manual': self.sortex_rate,  # Initialize with current automatic rate
            'sortex_cost_last_modified_by': self.env.user.id,
            'sortex_cost_last_modified_date': fields.Datetime.now(),
        })

        # Force immediate recalculation
        self._compute_sortex_landed_cost()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Sortex rate override mode enabled. You can now edit the sortex rate manually.'),
                'sticky': False,
                'type': 'success',
            }
        }

    def action_disable_hamli_override(self):
        """Disable hamli rate override mode and return to automatic calculation"""
        self.ensure_one()

        self.write({
            'hamli_cost_override_mode': False,
            'hamli_rate_manual': 0.0,
            'hamli_cost_last_modified_by': self.env.user.id,
            'hamli_cost_last_modified_date': fields.Datetime.now(),
        })

        # Force immediate recalculation to automatic value
        self._compute_hamali_cost()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Hamli rate override disabled. Automatic calculation restored.'),
                'sticky': False,
                'type': 'success',
            }
        }

    def action_disable_sortex_override(self):
        """Disable sortex rate override mode and return to automatic calculation"""
        self.ensure_one()

        self.write({
            'sortex_cost_override_mode': False,
            'sortex_rate_manual': 0.0,
            'sortex_cost_last_modified_by': self.env.user.id,
            'sortex_cost_last_modified_date': fields.Datetime.now(),
        })

        # Force immediate recalculation to automatic value
        self._compute_sortex_landed_cost()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Sortex rate override disabled. Automatic calculation restored.'),
                'sticky': False,
                'type': 'success',
            }
        }

    @api.onchange('hamli_rate_manual')
    def _onchange_hamli_rate_manual(self):
        """Update audit trail when hamli rate is manually changed"""
        if self.hamli_cost_override_mode and self.hamli_rate_manual:
            self.hamli_cost_last_modified_by = self.env.user.id
            self.hamli_cost_last_modified_date = fields.Datetime.now()

    @api.onchange('sortex_rate_manual')
    def _onchange_sortex_rate_manual(self):
        """Update audit trail when sortex rate is manually changed"""
        if self.sortex_cost_override_mode and self.sortex_rate_manual:
            self.sortex_cost_last_modified_by = self.env.user.id
            self.sortex_cost_last_modified_date = fields.Datetime.now()

    def write(self, vals):
        """Override write to update audit trail and trigger recalculation"""
        # Track manual rate changes
        if 'hamli_rate_manual' in vals:
            vals.update({
                'hamli_cost_last_modified_by': self.env.user.id,
                'hamli_cost_last_modified_date': fields.Datetime.now(),
            })

        if 'sortex_rate_manual' in vals:
            vals.update({
                'sortex_cost_last_modified_by': self.env.user.id,
                'sortex_cost_last_modified_date': fields.Datetime.now(),
            })

        result = super(MrpProduction, self).write(vals)

        # Force recalculation of hamali_cost and sortex_landed_cost when override values change
        if any(field in vals for field in ['hamli_rate_manual', 'sortex_rate_manual',
                                          'hamli_cost_override_mode', 'sortex_cost_override_mode']):
            _logger.info("Triggering cost recalculation for MO %s due to override changes", self.name)

            # Force recomputation of the overridden fields
            self._compute_hamali_cost()
            self._compute_sortex_landed_cost()

            # This will automatically trigger recalculation of dependent costs
            # due to the @api.depends decorators on other computed fields

        return result



    # Override the original hamali_cost and sortex_landed_cost fields to use manual rates
    @api.depends('total_weight', 'state', 'hamali_rate', 'hamli_cost_override_mode', 'hamli_rate_manual')
    def _compute_hamali_cost(self):
        """Override hamali cost computation to use manual rate when enabled"""
        for record in self:
            if record.state in ('draft', 'cancel'):
                record.hamali_cost = 0.0
                continue

            if record.hamli_cost_override_mode and record.hamli_rate_manual:
                # Use manual rate × total weight
                record.hamali_cost = record.total_weight * record.hamli_rate_manual
                _logger.info(f'Using manual hamali rate for {record.name}: {record.hamli_rate_manual} × {record.total_weight} = {record.hamali_cost}')
            else:
                # Use automatic calculation (original logic)
                record.hamali_cost = record.total_weight * record.hamali_rate
                _logger.info(f'Using automatic hamali rate for {record.name}: {record.hamali_rate} × {record.total_weight} = {record.hamali_cost}')

    @api.depends('total_weight', 'state', 'is_production_b', 'sortex_rate', 'sortex_cost_override_mode', 'sortex_rate_manual')
    def _compute_sortex_landed_cost(self):
        """Override sortex landed cost computation to use manual rate when enabled"""
        for record in self:
            if record.state in ('draft', 'cancel') or record.is_production_b:
                record.sortex_landed_cost = 0.0
                continue

            if record.sortex_cost_override_mode and record.sortex_rate_manual:
                # Use manual rate × total weight
                record.sortex_landed_cost = record.total_weight * record.sortex_rate_manual
                _logger.info(f'Using manual sortex rate for {record.name}: {record.sortex_rate_manual} × {record.total_weight} = {record.sortex_landed_cost}')
            else:
                # Use automatic calculation (original logic)
                record.sortex_landed_cost = record.total_weight * record.sortex_rate
                _logger.info(f'Using automatic sortex rate for {record.name}: {record.sortex_rate} × {record.total_weight} = {record.sortex_landed_cost}')

    # Property methods to ensure compatibility with existing code
    @property
    def effective_hamali_cost(self):
        """Return the effective hamali cost (final cost if override is enabled, otherwise computed cost)"""
        return self.hamli_cost_final if self.hamli_cost_override_mode else self.hamali_cost

    @property
    def effective_sortex_cost(self):
        """Return the effective sortex cost (final cost if override is enabled, otherwise computed cost)"""
        return self.sortex_cost_final if self.sortex_cost_override_mode else self.sortex_landed_cost


    @api.constrains('hamli_rate_manual', 'sortex_rate_manual')
    def _check_manual_rates(self):
        """Validate manual rate entries"""
        for record in self:
            if record.hamli_cost_override_mode and record.hamli_rate_manual < 0:
                raise ValidationError(_('Manual hamli rate cannot be negative.'))

            if record.sortex_cost_override_mode and record.sortex_rate_manual < 0:
                raise ValidationError(_('Manual sortex rate cannot be negative.'))
