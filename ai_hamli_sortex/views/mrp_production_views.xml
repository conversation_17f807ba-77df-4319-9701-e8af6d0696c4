<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Manufacturing Order Form View - Add Hamli Sortex Cost Override Section -->
        <record id="view_mrp_production_form_hamli_sortex_override" model="ir.ui.view">
            <field name="name">mrp.production.form.hamli.sortex.override</field>
            <field name="model">mrp.production</field>
            <field name="inherit_id" ref="ai_bt_spices_module.mrp_production_subcontract_form_inherit"/>
            <field name="arch" type="xml">

                <!-- Add new section for Hamli & Sortex Cost Override to the Hamali & Sortex Rate Configuration page -->
                <xpath expr="//field[@name='hamali_rate']/.." position="after">
                    <group string="Hamli &amp; Sortex Rate Override" groups="ai_hamli_sortex.group_hamli_sortex_cost_editor">

                        <!-- Hamli Rate Override Section -->
                        <group string="Hamli Rate Override" col="4">
                            <field name="hamli_cost_override_mode" widget="boolean_toggle"/>
                            <button name="action_enable_hamli_override"
                                    string="Enable Override"
                                    type="object"
                                    class="btn-primary"
                                    invisible="hamli_cost_override_mode"
                                    groups="ai_hamli_sortex.group_hamli_sortex_cost_editor"/>
                            <button name="action_disable_hamli_override"
                                    string="Disable Override"
                                    type="object"
                                    class="btn-secondary"
                                    invisible="not hamli_cost_override_mode"
                                    groups="ai_hamli_sortex.group_hamli_sortex_cost_editor"/>
                            <newline/>

                            <label for="hamali_rate" string="Automatic Hamli Rate"/>
                            <div>
                                <field name="hamali_rate" readonly="1" class="text-muted"/>
                                <span class="text-muted"> per kg</span>
                            </div>

                            <field name="hamli_rate_manual"
                                   string="Manual Hamli Rate (per kg)"
                                   readonly="not hamli_cost_override_mode"
                                   required="hamli_cost_override_mode"/>
                            <newline/>

                            <label for="hamali_cost" string="Current Hamli Cost"/>
                            <div>
                                <field name="hamali_cost" readonly="1" class="fw-bold"/>
                                <span invisible="not hamli_cost_override_mode" class="badge badge-warning ms-2">Manual Rate</span>
                                <span invisible="hamli_cost_override_mode" class="badge badge-info ms-2">Automatic Rate</span>
                                <span class="text-muted"> (Rate × <field name="total_weight" readonly="1"/> kg)</span>
                            </div>
                            <newline/>
                        </group>
                        
                        <separator/>
                        
                        <!-- Sortex Rate Override Section -->
                        <group string="Sortex Rate Override" col="4">
                            <field name="sortex_cost_override_mode" widget="boolean_toggle"/>
                            <button name="action_enable_sortex_override"
                                    string="Enable Override"
                                    type="object"
                                    class="btn-primary"
                                    invisible="sortex_cost_override_mode or is_production_b"
                                    groups="ai_hamli_sortex.group_hamli_sortex_cost_editor"/>
                            <button name="action_disable_sortex_override"
                                    string="Disable Override"
                                    type="object"
                                    class="btn-secondary"
                                    invisible="not sortex_cost_override_mode"
                                    groups="ai_hamli_sortex.group_hamli_sortex_cost_editor"/>
                            <newline/>

                            <label for="sortex_rate" string="Automatic Sortex Rate"/>
                            <div>
                                <field name="sortex_rate" readonly="1" class="text-muted"/>
                                <span class="text-muted" invisible="is_production_b"> per kg</span>
                                <span class="text-muted" invisible="not is_production_b"> (Production B - No Sortex Cost)</span>
                            </div>

                            <field name="sortex_rate_manual"
                                   string="Manual Sortex Rate (per kg)"
                                   readonly="not sortex_cost_override_mode or is_production_b"
                                   required="sortex_cost_override_mode and not is_production_b"/>
                            <newline/>

                            <label for="sortex_landed_cost" string="Current Sortex Cost"/>
                            <div>
                                <field name="sortex_landed_cost" readonly="1" class="fw-bold"/>
                                <span invisible="not sortex_cost_override_mode" class="badge badge-warning ms-2">Manual Rate</span>
                                <span invisible="sortex_cost_override_mode" class="badge badge-info ms-2">Automatic Rate</span>
                                <span class="text-muted" invisible="is_production_b"> (Rate × <field name="total_weight" readonly="1"/> kg)</span>
                            </div>
                            <newline/>
                        </group>
                        
                        <separator/>
                        
                        <!-- Cost Impact Summary -->
                        <group string="Cost Impact Summary" col="4">
                            <label for="total_cost" string="Total Cost"/>
                            <div>
                                <field name="total_cost" readonly="1" class="fw-bold text-primary"/>
                                <span class="text-muted"> (includes current hamli and sortex costs)</span>
                            </div>

                            <label for="ci_landed_cost" string="CI Landed Cost"/>
                            <div>
                                <field name="ci_landed_cost" readonly="1" class="fw-bold"/>
                                <span class="text-muted"> (4.5% of base costs including current hamli/sortex)</span>
                            </div>
                        </group>

                        <separator/>

                        <!-- Audit Trail Section -->
                        <group string="Audit Trail" groups="ai_hamli_sortex.group_hamli_sortex_cost_manager">
                            <group string="Hamli Cost Changes" invisible="not hamli_cost_last_modified_by">
                                <field name="hamli_cost_last_modified_by" readonly="1" string="Last Modified By"/>
                                <field name="hamli_cost_last_modified_date" readonly="1" string="Last Modified Date"/>
                            </group>
                            <group string="Sortex Cost Changes" invisible="not sortex_cost_last_modified_by">
                                <field name="sortex_cost_last_modified_by" readonly="1" string="Last Modified By"/>
                                <field name="sortex_cost_last_modified_date" readonly="1" string="Last Modified Date"/>
                            </group>
                        </group>

                        <!-- Help Text -->
                        <div class="alert alert-info" role="alert">
                            <h6><i class="fa fa-info-circle"/> How to use Rate Override:</h6>
                            <ul class="mb-0">
                                <li><strong>Enable Override:</strong> Click "Enable Override" to switch from automatic to manual rate calculation</li>
                                <li><strong>Edit Rates:</strong> Enter your desired hamli or sortex rate per kg in the manual fields</li>
                                <li><strong>Cost Calculation:</strong> Final cost = Manual Rate × Total Weight (kg)</li>
                                <li><strong>Automatic Recalculation:</strong> Total cost and CI landed cost will automatically update based on your manual rates</li>
                                <li><strong>Disable Override:</strong> Click "Disable Override" to return to automatic rate calculation</li>
                                <li><strong>Production B:</strong> Sortex rates are not applicable for Production B orders</li>
                                <li><strong>Any State:</strong> Rate overrides can be enabled and modified in any manufacturing order state</li>
                                <li><strong>Post-Production:</strong> Especially useful for adjusting rates after manufacturing is completed</li>
                            </ul>
                        </div>

                    </group>
                </xpath>
                
            </field>
        </record>

    </data>
</odoo>
