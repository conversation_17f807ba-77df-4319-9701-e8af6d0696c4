import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
    """
    Remove any constraints that prevent duplicate product-location combinations
    in sale order lines.
    """
    _logger.info("Starting migration to remove duplicate product-location constraints")
    
    try:
        # Find and remove any unique constraints that might prevent duplicate product-location combinations
        cr.execute("""
            SELECT 
                tc.constraint_name,
                string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) as columns
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
            WHERE 
                tc.table_name = 'sale_order_line'
                AND tc.constraint_type = 'UNIQUE'
            GROUP BY tc.constraint_name
            HAVING string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) LIKE '%product_id%'
               AND string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) LIKE '%location%'
        """)
        
        constraints_to_remove = cr.fetchall()
        
        for constraint_name, columns in constraints_to_remove:
            try:
                _logger.info(f"Removing constraint: {constraint_name} on columns: {columns}")
                cr.execute(f"ALTER TABLE sale_order_line DROP CONSTRAINT IF EXISTS {constraint_name}")
                _logger.info(f"Successfully removed constraint: {constraint_name}")
            except Exception as e:
                _logger.warning(f"Could not remove constraint {constraint_name}: {e}")
        
        # Also check for any PostgreSQL-specific constraints
        cr.execute("""
            SELECT conname, pg_get_constraintdef(oid) as definition
            FROM pg_constraint 
            WHERE conrelid = 'sale_order_line'::regclass
            AND contype = 'u'
            AND pg_get_constraintdef(oid) LIKE '%product_id%'
            AND pg_get_constraintdef(oid) LIKE '%location%'
        """)
        
        pg_constraints = cr.fetchall()
        
        for constraint_name, definition in pg_constraints:
            try:
                _logger.info(f"Removing PostgreSQL constraint: {constraint_name} - {definition}")
                cr.execute(f"ALTER TABLE sale_order_line DROP CONSTRAINT IF EXISTS {constraint_name}")
                _logger.info(f"Successfully removed PostgreSQL constraint: {constraint_name}")
            except Exception as e:
                _logger.warning(f"Could not remove PostgreSQL constraint {constraint_name}: {e}")
        
        # Commit the changes
        cr.commit()
        _logger.info("Migration completed successfully")
        
    except Exception as e:
        _logger.error(f"Error during migration: {e}")
        cr.rollback()
        raise e
