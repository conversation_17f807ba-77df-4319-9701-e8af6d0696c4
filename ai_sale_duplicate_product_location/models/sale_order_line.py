from odoo import models, fields, api
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    
    # Override SQL constraints to remove any that prevent duplicate product-location combinations
    _sql_constraints = []
    
    @api.model_create_multi
    def create(self, vals_list):
        """
        Override create method to bypass any validation that prevents 
        duplicate product-location combinations in the same sale order.
        """
        try:
            # Call the parent create method
            lines = super(SaleOrderLine, self).create(vals_list)
            _logger.info("Successfully created sale order lines with potential duplicate product-location combinations")
            return lines
        except Exception as e:
            # If there's still a constraint error, try to handle it
            error_msg = str(e).lower()
            if 'duplicate' in error_msg or 'unique' in error_msg or 'constraint' in error_msg:
                _logger.warning(f"Constraint error detected: {e}")
                # Try to create lines one by one to identify the problematic constraint
                lines = self.env['sale.order.line']
                for vals in vals_list:
                    try:
                        line = super(<PERSON><PERSON><PERSON>r<PERSON><PERSON>, self).create([vals])
                        lines |= line
                    except Exception as individual_error:
                        _logger.error(f"Failed to create individual line: {individual_error}")
                        # If it's a constraint error, we need to modify the approach
                        if 'duplicate' in str(individual_error).lower():
                            # Remove any problematic constraint temporarily
                            self._remove_duplicate_constraints()
                            line = super(SaleOrderLine, self).create([vals])
                            lines |= line
                        else:
                            raise individual_error
                return lines
            else:
                raise e
    
    def _remove_duplicate_constraints(self):
        """
        Remove any database constraints that prevent duplicate product-location combinations.
        This is a more aggressive approach to handle the constraint issue.
        """
        try:
            # Check for and remove any unique constraints on product_id + line_location_id + order_id
            self.env.cr.execute("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'sale_order_line' 
                AND constraint_type = 'UNIQUE'
                AND constraint_name LIKE '%product%'
                OR constraint_name LIKE '%location%'
            """)
            
            constraints = self.env.cr.fetchall()
            for constraint in constraints:
                constraint_name = constraint[0]
                try:
                    self.env.cr.execute(f"ALTER TABLE sale_order_line DROP CONSTRAINT IF EXISTS {constraint_name}")
                    _logger.info(f"Removed constraint: {constraint_name}")
                except Exception as e:
                    _logger.warning(f"Could not remove constraint {constraint_name}: {e}")
                    
        except Exception as e:
            _logger.warning(f"Error in _remove_duplicate_constraints: {e}")
    
    @api.constrains('product_id', 'line_location_id', 'order_id')
    def _check_duplicate_product_location(self):
        """
        Override any constraint that prevents duplicate product-location combinations.
        This method intentionally does nothing to allow duplicates.
        """
        # Intentionally empty - allows duplicate product-location combinations
        pass
    
    def write(self, vals):
        """
        Override write method to handle any constraint issues during updates.
        """
        try:
            return super(SaleOrderLine, self).write(vals)
        except Exception as e:
            error_msg = str(e).lower()
            if 'duplicate' in error_msg or 'unique' in error_msg or 'constraint' in error_msg:
                _logger.warning(f"Constraint error during write: {e}")
                # Try to remove constraints and retry
                self._remove_duplicate_constraints()
                return super(SaleOrderLine, self).write(vals)
            else:
                raise e
