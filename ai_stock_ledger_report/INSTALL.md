# Installation Guide for AI Stock Ledger Report

## Prerequisites
- Odoo 18.0 or later
- Python packages: xlsxwriter (install with: `pip install xlsxwriter`)
- Required Odoo modules: stock, stock_account, purchase, sale

## Install Python Dependencies
Before installing the module, ensure xlsxwriter is installed:
```bash
pip install xlsxwriter
```

Or install from the requirements file:
```bash
pip install -r ai_stock_ledger_report/requirements.txt
```

## Installation Steps

1. **Copy Module to Addons Directory**
   ```bash
   cp -r ai_stock_ledger_report /path/to/your/odoo/addons/
   ```

2. **Update Apps List**
   - Go to Apps menu in Odoo
   - Click "Update Apps List"
   - Search for "AI Stock Ledger Report"

3. **Install Module**
   - Click "Install" on the AI Stock Ledger Report module
   - Wait for installation to complete

4. **Verify Installation**
   - Go to Inventory > Reporting
   - Look for "Stock Ledger Report" menu item

## Usage

1. **Access the Report**
   - Navigate to Inventory > Reporting > Stock Ledger Report

2. **Configure Report Parameters**
   - Select date range (From Date and To Date)
   - Choose warehouses (required)
   - Optionally filter by specific products

3. **Generate Report**
   - Click "Export to Excel"
   - The report will be downloaded as an Excel file

## Troubleshooting

### Common Issues

1. **Module Not Visible**
   - Ensure the module is in the correct addons directory
   - Update the apps list
   - Check if all dependencies are installed

2. **Excel Export Not Working**
   - Verify xlsxwriter is installed: `pip install xlsxwriter`
   - Check browser settings for file downloads
   - Test imports by running: `python ai_stock_ledger_report/test_imports.py`

3. **Import Errors (cannot import serialize_exception)**
   - This error has been fixed in the latest version
   - Update to the latest module version
   - Restart Odoo server after updating

3. **No Data in Report**
   - Verify stock moves exist for the selected date range
   - Check warehouse and product filters
   - Ensure stock moves are in 'done' state

4. **Products Not Loading in Selection**
   - Check if products exist and are active
   - Verify product types are 'Storable Product' or 'Consumable'
   - Use the "Debug Products" button in the wizard
   - See TROUBLESHOOTING.md for detailed solutions

### Support
For issues or questions, please check the module documentation or contact the system administrator.
