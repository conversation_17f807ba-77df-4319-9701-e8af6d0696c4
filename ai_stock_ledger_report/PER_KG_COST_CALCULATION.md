# Per KG Cost vs Rate Calculation

## Problem Fixed
Previously, both "PER KG COST" and "RATE" columns showed the same value, which was incorrect.

## Correct Logic Now Implemented

### Column Definitions
- **RATE**: The actual transaction price per unit (as recorded in the stock move)
- **PER KG COST**: The cost calculated per kilogram of the product

### Calculation Methods

#### Method 1: Product Weight Based
```python
if product_weight and product_weight > 0:
    per_kg_cost = rate / product_weight
```
**Example**: 
- Product: Rice Bag
- Rate: ₹100 per bag
- Product Weight: 25 kg
- Per KG Cost: ₹100 ÷ 25 = ₹4.00 per kg

#### Method 2: UOM Category Based
```python
if 'weight' in uom_category or 'kg' in uom_name:
    per_kg_cost = rate  # Rate is already per kg
```
**Example**:
- Product: Wheat
- Rate: ₹30 per kg
- UOM: kg
- Per KG Cost: ₹30.00 per kg (same as rate)

#### Method 3: Product Name Pattern Recognition
```python
weight_match = re.search(r'\[(\d+)KG\]', product_name)
if weight_match:
    package_weight = float(weight_match.group(1))
    per_kg_cost = rate / package_weight
```
**Example**:
- Product: "JEERA KACHO NO.1 [50KG]"
- Rate: ₹2500 per bag
- Extracted Weight: 50 kg
- Per KG Cost: ₹2500 ÷ 50 = ₹50.00 per kg

#### Method 4: Fallback
```python
per_kg_cost = rate / (product_weight or 1.0)
```
**Example**:
- Product: Unknown weight product
- Rate: ₹100 per unit
- Default Weight: 1.0
- Per KG Cost: ₹100.00 per kg

## Real-World Examples

### Example 1: JEERA KACHO NO.1 [50KG]
- **Transaction**: Purchase 10 bags at ₹2500 per bag
- **Rate**: ₹2500.00 (per bag)
- **Per KG Cost**: ₹50.00 (₹2500 ÷ 50 kg)
- **Amount**: ₹25,000 (10 bags × ₹2500)

### Example 2: METHI LAXMI [10KG]
- **Transaction**: Sale 5 bags at ₹750 per bag
- **Rate**: ₹750.00 (per bag)
- **Per KG Cost**: ₹75.00 (₹750 ÷ 10 kg)
- **Amount**: ₹3,750 (5 bags × ₹750)

### Example 3: Loose Rice (per kg)
- **Transaction**: Purchase 100 kg at ₹45 per kg
- **Rate**: ₹45.00 (per kg)
- **Per KG Cost**: ₹45.00 (same as rate, already per kg)
- **Amount**: ₹4,500 (100 kg × ₹45)

## Excel Report Columns

| DATE | VOUCHER NO. | ACCOUNT | LOT NUMBER | QTY REC | QTY ISS | QTY BAL | UNITS | **PER KG COST** | AMOUNT | UNIT ISS | **RATE** | AMOUNT | REMARK | UNITS BAL |
|------|-------------|---------|------------|---------|---------|---------|-------|-----------------|--------|----------|----------|--------|--------|-----------|
| 01/04/2025 | PUR-001 | SUPPLIER | LOT001 | 10 | | 10 | Bags | **₹50.00** | ₹25,000 | | **₹2,500.00** | ₹25,000 | Purchase | 10 |

## Debug Features

### Debug Costs Button
Use the "Debug Costs" button to see:
- Product names and their cost calculations
- Rate vs Per KG Cost comparison
- Calculation method used for each product
- Sample data for verification

### Verification Steps
1. Select warehouses and date range
2. Click "Debug Costs" to see calculation samples
3. Verify the logic makes sense for your products
4. Generate the full report

## Configuration Tips

### For Accurate Per KG Cost:
1. **Set Product Weight**: Go to product form and set the weight field
2. **Use Descriptive Names**: Include weight in product names like "[50KG]"
3. **Configure UOM**: Use weight-based units of measure when appropriate
4. **Check Calculations**: Use debug tools to verify calculations

### Common Issues:
- **Same values in both columns**: Product weight not set
- **Incorrect per kg cost**: Check product weight or name format
- **Zero per kg cost**: Product has no weight information

## Result
Now the report correctly shows:
- **PER KG COST**: Actual cost per kilogram
- **RATE**: Transaction rate per unit
- **Different values** that make business sense
- **Accurate cost analysis** for inventory management
