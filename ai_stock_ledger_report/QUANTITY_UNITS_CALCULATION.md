# Quantity and Units Calculation Logic

## Overview
Updated the stock ledger report to be consistent with your existing modules' quantity calculation logic, separating quantities and weights properly.

## Key Changes Made

### 1. Separate Quantity and Weight Tracking
**Before**: Only quantity columns
**After**: Both quantity and weight columns

### 2. New Column Structure
| Column | Description | Source |
|--------|-------------|---------|
| QTY REC | Quantity Received | `move.product_uom_qty` |
| QTY ISS | Quantity Issued | `move.product_uom_qty` |
| QTY BAL | Running Quantity Balance | Calculated |
| UNITS | Unit of Measure | `move.product_uom.name` |
| WEIGHT REC | Weight Received (Kg) | `qty * product.weight` |
| WEIGHT ISS | Weight Issued (Kg) | `qty * product.weight` |
| WEIGHT BAL | Running Weight Balance | Calculated |
| PER KG COST | Cost per Kg | Calculated |
| RATE | Transaction Rate | `move.price_unit` |
| AMOUNT | Total Amount | `qty * rate` |

## Calculation Logic (Consistent with Your Modules)

### 1. Base Quantity Calculation
```python
base_qty = move.product_uom_qty
total_weight = base_qty * move.product_id.weight if move.product_id.weight else base_qty
```

### 2. Direction-Based Assignment
```python
if incoming_to_warehouse:
    qty_received = base_qty
    weight_received = total_weight
elif outgoing_from_warehouse:
    qty_issued = base_qty
    weight_issued = total_weight
```

### 3. Running Balances
```python
running_qty_balance += qty_received - qty_issued
running_weight_balance += weight_received - weight_issued
```

## Examples Based on Your Business Logic

### Example 1: JEERA KACHO NO.1 [50KG] - Purchase
- **Transaction**: Purchase 10 bags
- **QTY REC**: 10 (bags)
- **WEIGHT REC**: 500 Kg (10 × 50)
- **UNITS**: Bags
- **RATE**: ₹2,500 per bag
- **PER KG COST**: ₹50 per kg
- **AMOUNT**: ₹25,000

### Example 2: METHI LAXMI [10KG] - Sale
- **Transaction**: Sale 5 bags
- **QTY ISS**: 5 (bags)
- **WEIGHT ISS**: 50 Kg (5 × 10)
- **UNITS**: Bags
- **RATE**: ₹750 per bag
- **PER KG COST**: ₹75 per kg
- **AMOUNT**: ₹3,750

### Example 3: Loose Rice - Purchase
- **Transaction**: Purchase 100 kg
- **QTY REC**: 100 (kg)
- **WEIGHT REC**: 100 Kg (same as quantity)
- **UNITS**: Kg
- **RATE**: ₹45 per kg
- **PER KG COST**: ₹45 per kg (same as rate)
- **AMOUNT**: ₹4,500

## Consistency with Your Existing Modules

### 1. Weight Calculation Pattern
Matches your existing pattern:
```python
# From ai_bt_spices_module/models/stock.py
total_weight = move.product_uom_qty * move.product_id.weight

# From ai_bt_spices_module/models/stock_quant.py
lot_weight = product_id.weight * quantity
```

### 2. Quantity vs Weight Distinction
Follows your existing logic:
- **Quantity**: Number of units (bags, pieces, kg)
- **Weight**: Total weight in kg
- **Units**: The unit of measure for quantity

### 3. Cost Calculations
Consistent with your cost tracking:
```python
# Similar to your existing cost calculations
per_kg_cost = rate / product_weight  # When rate is per unit
total_cost = quantity * rate
```

## Business Benefits

### 1. Clear Inventory Tracking
- **Quantity tracking**: Know how many units (bags/pieces)
- **Weight tracking**: Know total weight for logistics
- **Separate balances**: Track both quantity and weight balances

### 2. Better Cost Analysis
- **Per unit costs**: Rate shows cost per bag/piece
- **Per kg costs**: Per KG Cost shows standardized cost
- **Weight-based pricing**: Important for bulk commodities

### 3. Operational Efficiency
- **Warehouse management**: Track both units and weights
- **Transportation planning**: Use weight data
- **Inventory valuation**: Use both quantity and weight metrics

## Debug Features

### "Debug Costs" Button Output
Now shows:
```
Product: JEERA KACHO NO.1 [50KG]
  - Qty Rec/Iss: 10.00/0.00 Bags
  - Weight Rec/Iss: 500.00/0.00 Kg
  - Rate: 2500.00
  - Per KG Cost: 50.00
  - Amount: 25000.00
```

## Report Layout

The Excel report now clearly separates:
1. **Quantity columns**: QTY REC, QTY ISS, QTY BAL, UNITS
2. **Weight columns**: WEIGHT REC, WEIGHT ISS, WEIGHT BAL (all in Kg)
3. **Cost columns**: PER KG COST, RATE, AMOUNT
4. **Reference columns**: DATE, VOUCHER, ACCOUNT, LOT, REMARK

This provides complete visibility into both unit-based and weight-based inventory movements, consistent with your existing business processes!
