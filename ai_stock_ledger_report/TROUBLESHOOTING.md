# Troubleshooting Guide - AI Stock Ledger Report

## Products Not Loading in Selection

### Problem
Products are not appearing in the product selection field when trying to configure the report.

### Possible Causes & Solutions

#### 1. **Product Type Filter**
**Cause**: The domain filter might be excluding your products.
**Solution**: 
- The module now includes both 'product' and 'consu' (consumable) types
- Check if your products have the correct type set

#### 2. **Inactive Products**
**Cause**: Products might be archived/inactive.
**Solution**:
- Go to Inventory > Products > Products
- Check if your products are active
- Use the filter to show archived products if needed

#### 3. **No Products in System**
**Cause**: No products exist in the system.
**Solution**:
- Create some test products first
- Go to Inventory > Products > Products > Create

#### 4. **Permission Issues**
**Cause**: User doesn't have permission to view products.
**Solution**:
- Check user has Inventory/User or Inventory/Manager rights
- Verify access rights in Settings > Users & Companies > Users

#### 5. **Database/Cache Issues**
**Cause**: Odoo cache or database issues.
**Solution**:
- Restart Odoo server
- Update module list
- Clear browser cache

### Debug Steps

1. **Use Debug Button**
   - Open the Stock Ledger Report wizard
   - Click "Debug Products" button
   - Check the notification for available products

2. **Check Product Count**
   ```python
   # In Odoo shell or debug mode
   products = env['product.product'].search([('type', 'in', ['product', 'consu']), ('active', '=', True)])
   print(f"Found {len(products)} products")
   ```

3. **Manual Product Check**
   - Go to Inventory > Products > Products
   - Verify products exist and are active
   - Note the product types

4. **Check Domain Filter**
   - The current domain is: `[('type', 'in', ['product', 'consu']), ('active', '=', True)]`
   - This should include most products

### Alternative Solutions

#### Option 1: Remove Product Filter Temporarily
Edit the wizard and remove the domain filter:
```python
product_ids = fields.Many2many(
    'product.product',
    string='Products',
    # domain=[('type', 'in', ['product', 'consu']), ('active', '=', True)],  # Comment this line
    help="Leave empty to include all products"
)
```

#### Option 2: Use Different Widget
Try using a different widget in the view:
```xml
<field name="product_ids" widget="many2many_checkboxes"/>
```

#### Option 3: Manual Product Selection
If the many2many field doesn't work, you can:
1. Leave the product field empty (this will include all products)
2. Filter by warehouse only (locations are automatically included based on warehouse)

### Contact Support
If none of these solutions work:
1. Check Odoo logs for errors
2. Verify module installation
3. Test with a fresh database
4. Contact your system administrator

### Quick Test
To quickly test if the issue is with the product field:
1. Leave the product field empty
2. Select a warehouse
3. Try to generate the report
4. If it works, the issue is specifically with the product selection field
