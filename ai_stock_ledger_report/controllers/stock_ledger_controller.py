# -*- coding: utf-8 -*-

import json
import logging
from odoo import http
from odoo.http import content_disposition, request

_logger = logging.getLogger(__name__)


class StockLedgerController(http.Controller):
    """Controller for Stock Ledger Report Excel Export"""

    @http.route('/xlsx_reports', type='http', auth='user', methods=['POST'], csrf=False)
    def get_report_xlsx(self, model, options, output_format, report_name):
        """Generate and download Excel report"""
        try:
            _logger.info(f'Generating report - Model: {model}, Format: {output_format}, Name: {report_name}')

            # Validate parameters
            if not model:
                raise ValueError("Model parameter is required")
            if not options:
                raise ValueError("Options parameter is required")

            # Get report object
            report_obj = request.env[model].with_user(request.session.uid)

            # Parse options
            try:
                if isinstance(options, str):
                    parsed_options = json.loads(options)
                else:
                    parsed_options = options
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in options: {str(e)}")

            _logger.info(f'Parsed options: {parsed_options}')

            token = 'dummy-because-api-expects-one'

            if output_format == 'xlsx':
                response = request.make_response(
                    None,
                    headers=[
                        ('Content-Type', 'application/vnd.ms-excel'),
                        ('Content-Disposition', content_disposition(f'{report_name}.xlsx'))
                    ]
                )

                # Generate the report
                report_obj.get_xlsx_report(parsed_options, response)
                response.set_cookie('fileToken', token)

                _logger.info('Report generated successfully')
                return response
            else:
                raise ValueError(f"Unsupported output format: {output_format}")

        except Exception as e:
            _logger.error('Error generating stock ledger report: %s', str(e), exc_info=True)
            error = {
                'code': 500,
                'message': 'Error generating report',
                'data': str(e)
            }
            return request.make_response(
                json.dumps(error),
                headers=[('Content-Type', 'application/json')]
            )
