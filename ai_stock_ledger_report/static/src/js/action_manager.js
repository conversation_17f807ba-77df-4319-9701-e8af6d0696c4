/** @odoo-module **/

import { registry } from "@web/core/registry";
import { download } from "@web/core/network/download";

registry.category("ir.actions.report handlers").add("stock_ledger_xlsx", async function (action) {
    console.log('Stock Ledger Report Action:', action);

    if (action.report_type === 'stock_ledger_xlsx') {
        try {
            // Validate action data
            if (!action.data) {
                console.error('Action data is missing:', action);
                throw new Error('Report data is missing');
            }

            const actionOptions = action.data;

            // Validate required fields
            if (!actionOptions.model || !actionOptions.options) {
                console.error('Missing required action options:', actionOptions);
                throw new Error('Missing required report parameters');
            }

            const url = `/xlsx_reports`;
            const downloadData = {
                model: actionOptions.model,
                options: actionOptions.options,
                output_format: actionOptions.output_format || 'xlsx',
                report_name: actionOptions.report_name || 'Stock Ledger Report',
            };

            console.log('Download data:', downloadData);

            const downloadOptions = {
                data: downloadData,
                complete: () => {
                    console.log('Download completed successfully');
                },
                error: (error) => {
                    console.error('Error downloading report:', error);
                },
            };

            return download(url, downloadOptions.data, downloadOptions);

        } catch (error) {
            console.error('Error in stock ledger report handler:', error);
            throw error;
        }
    }
});
