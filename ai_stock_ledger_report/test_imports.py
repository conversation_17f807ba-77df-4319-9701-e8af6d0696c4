#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify all imports work correctly
"""

def test_imports():
    """Test all module imports"""
    try:
        # Test basic Python imports
        import io
        import json
        import datetime
        import pytz
        print("✅ Basic Python imports: OK")
        
        # Test xlsxwriter import
        try:
            import xlsxwriter
            print("✅ xlsxwriter import: OK")
        except ImportError:
            print("❌ xlsxwriter import: FAILED - Please install: pip install xlsxwriter")
            return False
        
        # Test if we can create a basic workbook
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet('Test')
        sheet.write(0, 0, 'Test')
        workbook.close()
        print("✅ xlsxwriter functionality: OK")
        
        print("\n🎉 All imports and basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

if __name__ == "__main__":
    test_imports()
