#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple syntax test for the stock ledger report module
"""

def test_syntax():
    """Test if the Python files have correct syntax"""
    import ast
    import os
    
    files_to_test = [
        'wizard/stock_ledger_report.py',
        'controllers/stock_ledger_controller.py',
        '__init__.py',
        'wizard/__init__.py',
        'controllers/__init__.py'
    ]
    
    print("🔍 Testing Python syntax...")
    
    for file_path in files_to_test:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                ast.parse(content)
                print(f"✅ {file_path}: Syntax OK")
            except SyntaxError as e:
                print(f"❌ {file_path}: Syntax Error - {e}")
                return False
            except Exception as e:
                print(f"⚠️  {file_path}: Error reading file - {e}")
        else:
            print(f"⚠️  {file_path}: File not found")
    
    print("\n🎉 All Python files have correct syntax!")
    return True

if __name__ == "__main__":
    # Change to module directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    test_syntax()
