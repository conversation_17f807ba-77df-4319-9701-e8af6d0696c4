<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Stock Ledger Report Wizard Form View -->
    <record id="view_stock_ledger_report_wizard" model="ir.ui.view">
        <field name="name">stock.ledger.report.wizard.form</field>
        <field name="model">stock.ledger.report</field>
        <field name="arch" type="xml">
            <form string="Stock Ledger Report">
                <group>
                    <group string="Date Range">
                        <field name="date_from" required="1"/>
                        <field name="date_to" required="1"/>
                    </group>
                    <group string="Filters">
                        <field name="warehouse_ids" widget="many2many_tags" required="1"
                               placeholder="Select warehouses..."/>
                        <field name="product_ids" widget="many2many_tags"
                               placeholder="Select products (leave empty for all)..."
                               options="{'no_create': True, 'no_open': True}"/>
                    </group>
                </group>
                <footer>
                    <button name="export_excel_direct" type="object" string="Export to Excel"
                            class="oe_highlight" icon="fa-download"/>
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Stock Ledger Report Action -->
    <record id="action_stock_ledger_report" model="ir.actions.act_window">
        <field name="name">Stock Ledger Report</field>
        <field name="res_model">stock.ledger.report</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_stock_ledger_report_wizard"/>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_stock_ledger_report"
              name="Stock Ledger Report"
              parent="stock.menu_warehouse_report"
              action="action_stock_ledger_report"
              sequence="10"/>
</odoo>
